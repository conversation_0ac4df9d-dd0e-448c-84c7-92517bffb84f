package com.ruoyi.api.controller;

import java.text.ParseException;
import java.util.HashMap;
import java.util.List;

import com.alibaba.fastjson.JSON;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.system.domain.HtOrderRecord;
import com.ruoyi.system.domain.vo.HtOrderRecordVo;
import com.ruoyi.system.domain.vo.HtOrderVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.system.domain.HtOrder;
import com.ruoyi.system.service.IHtOrderService;
import com.ruoyi.common.core.page.TableDataInfo;

import javax.validation.Valid;

/**
 * 订单Controller
 *
 * <AUTHOR>
 * @date 2024-11-23
 */
@RestController
@RequestMapping("/api/order")
public class ApiHtOrderController extends BaseController {
    @Autowired
    private IHtOrderService htOrderService;

    //今日收支
    @GetMapping("/getTodayMoney/{userId}")
    public AjaxResult getTodayMoney(@PathVariable("userId") Integer userId){
        HashMap<String, Object> orderList =htOrderService.getTodayMoney(userId);
        return AjaxResult.success(orderList);
    }
    //往日收支
    @GetMapping("/getPastMoney/{userId}")
    public AjaxResult getPastMoney(@PathVariable("userId") Long userId) {
        HashMap<String, Object>  orderList =htOrderService.getPastMoney(userId);
        return AjaxResult.success(orderList);
    }

    /**
     * 查询订单列表
     */
    @GetMapping("/list")
    public TableDataInfo list(HtOrder htOrder) {
        startPage();
        List<HtOrder> list = htOrderService.selectHtOrderList(htOrder);
        return getDataTable(list);
    }


    /**
     * 获取订单详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(htOrderService.selectHtOrderById(id));
    }

    /**
     * 新增订单
     */
    @PostMapping
    public AjaxResult add(@RequestBody HtOrder htOrder) {
        return toAjax(htOrderService.insertHtOrder(htOrder));
    }

    /**
     * 修改订单
     */
    @PutMapping
    public AjaxResult edit(@RequestBody HtOrder htOrder) {
        return toAjax(htOrderService.updateHtOrder(htOrder));
    }

    /**
     * 删除订单
     */
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(htOrderService.deleteHtOrderByIds(ids));
    }

    /**
     * 获取付款详细信息
     */
    @GetMapping(value = "/getPayInfo/{userId}")
    public AjaxResult getPayInfo(@PathVariable("userId") Integer userId) {
        HashMap<String, Object> list=  htOrderService.getPayInfo(userId);
        return success(list);
    }


    /**
     * 修改订单状态
     * @return
     */
    @PostMapping("/updateOrderType")
    public AjaxResult updateOrderType(@RequestBody HtOrder htOrder){

        return toAjax(htOrderService.updateOrderType(htOrder));
    }

    /**
     * 委托寄售
     * @param id
     * @return
     */
    @GetMapping(value = "/detail/{id}")
    public AjaxResult get(@PathVariable("id") Long id) {
        return success(htOrderService.selectOrderById(id));
    }

    /**
     * 确认收款     根据子订单id修改订单状态
     * @param
     *
     */
    @Anonymous
    @GetMapping("/confirmPayment/{recordId}")
    public AjaxResult confirmPayment(@PathVariable Integer recordId){

        return htOrderService.confirmPayment(recordId);
    }
}
