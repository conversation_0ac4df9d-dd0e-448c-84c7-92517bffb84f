package com.ruoyi.quartz.task;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.HtGoods;
import com.ruoyi.system.domain.HtOrder;
import com.ruoyi.system.domain.HtOrderRecord;
import com.ruoyi.system.enums.OrderStatusEnum;
import com.ruoyi.system.mapper.HtGoodsMapper;
import com.ruoyi.system.mapper.HtOrderMapper;
import com.ruoyi.system.mapper.HtOrderRecordMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025-08-12
 * @description: 异步处理所有抢购完成的订单
 */
@Slf4j
@Component("OrderRecordTask")
public class OrderRecordTask {

    @Autowired
    private HtGoodsMapper htGoodsMapper;

    @Autowired
    private HtOrderMapper htOrderMapper;

    @Autowired
    private HtOrderRecordMapper htOrderRecordMapper;

    //根据今日抢购订单，进行算力拆分
    public void computingPower() {
        if(shouldTriggerAlgorithm()){
            executeAlgorithmAsync();
        }
    }

    /**
     * 检查是否需要触发算法
     */
    private boolean shouldTriggerAlgorithm() {
        List<HtGoods> goodsList = htGoodsMapper.selectList(Wrappers.<HtGoods>query()
                .eq("is_show", 1)
                .eq("is_del", 0));
        return goodsList.isEmpty();
    }

    /**
     * 执行算法
     */
    private void executeAlgorithmAsync() {
        try {
            //查询今天所有的订单
            List<HtOrder> orderList = htOrderMapper.selectList(Wrappers.<HtOrder>query()
                    .like("created_at", DateUtils.getDate()).eq("order_status",1));
            for(HtOrder htOrder:orderList){
                if(htOrder.getOrderType()==1){
                    htOrder.setOrderStatus(OrderStatusEnum.SPECIAL_STATUS.getCode());
                }
                if(htOrder.getOrderType()==3){
                    htOrder.setOrderStatus(OrderStatusEnum.NO_PAYMENT_REQUIRED.getCode());
                }
                //需要收款的订单，对需要付款金额重新进行拆分，匹配上多个收款订单，给多个人付款
                if(htOrder.getOrderType()==0){
                    //查询谁买了需要收款账号的商品
                    List<HtGoods> goodsList=htGoodsMapper.selectMyHtGoodsList(Long.valueOf(htOrder.getRecipientId()));
                    for(HtGoods htGoods:goodsList){
                        //查询最新一次的购买记录
                        HtOrder htOrderByGoodsId=htOrderMapper.selectOne(Wrappers.<HtOrder>query()
                                .orderByDesc("created_at").eq("goods_id", htGoods.getId()).last("limit 1"));
                        //查询最新一次的付款信息
                        HtOrderRecord htOrderRecord=htOrderRecordMapper.selectOne(Wrappers.<HtOrderRecord>query()
                                .orderByDesc("create_time").eq("user_id", htOrderByGoodsId.getUserId()).last("limit 1"));
                        //更新当前付款人的金额，付款金额需要扣除购买商品的所归属人 需要收款的金额
                        htOrderRecord.setAmount(htOrderRecord.getAmount().subtract(htOrder.getAmount()));
                        htOrderRecordMapper.updateById(htOrderRecord);

                        //查询当前订单的收款记录
                        HtOrderRecord htOrderRecordByCurrent=htOrderRecordMapper.selectOne(Wrappers.<HtOrderRecord>query()
                                .orderByDesc("create_time").eq("order_id", htOrder.getId()).last("limit 1"));
                        //改订单的收款人和下单用户一定是同一个人呢
                        if(StringUtils.isNotNull(htOrderRecordByCurrent)&&StringUtils.isNull(htOrderRecordByCurrent.getUserId())){
                            htOrderRecordByCurrent.setUserId(htOrderByGoodsId.getUserId().intValue());
                            htOrderRecordMapper.updateById(htOrderRecordByCurrent);
                        }
                    }
                    htOrder.setOrderStatus(OrderStatusEnum.PAID.getCode());
                }
                htOrderMapper.updateById(htOrder);
            }
        } catch (Exception e) {
            log.error("执行算法异常", e);
        }
    }
}

