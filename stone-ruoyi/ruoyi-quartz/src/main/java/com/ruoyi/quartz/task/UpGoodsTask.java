package com.ruoyi.quartz.task;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.system.domain.HtGoods;
import com.ruoyi.system.mapper.HtGoodsMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/12/6 23:21
 * @description: 涨价石头
 */
@Component("upGoodsTask")
public class UpGoodsTask {

    @Autowired
    private HtGoodsMapper htGoodsMapper;

    //石头上架
    public void goodsGrounding() {
        List<HtGoods> htGoods = htGoodsMapper.selectList(Wrappers.<HtGoods>query()
                .eq("is_hot", 1)
                .eq("is_del", 0));
        if (!htGoods.isEmpty()) {
            for (HtGoods htGood : htGoods) {
                htGood.setIsHot(0);
                htGood.setIsShow(1);
                htGoodsMapper.updateHtGoods(htGood);
            }
        }
    }
}

