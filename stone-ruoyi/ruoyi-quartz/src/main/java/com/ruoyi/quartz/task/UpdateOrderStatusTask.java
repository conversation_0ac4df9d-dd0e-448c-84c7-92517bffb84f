package com.ruoyi.quartz.task;

import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.HtOrder;
import com.ruoyi.system.mapper.HtOrderMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/12/6 11:39
 * @description: 修改订单状态
 */
@Component("UpdateOrderStatusTask")
public class UpdateOrderStatusTask {
    @Autowired
    private HtOrderMapper htOrderMapper;


    //修改订单状态为委托寄售
    public void updateOrderStatusTask() {
        HtOrder order = new HtOrder();
        order.setOrderStatus(3L);
        List<HtOrder> orderList = htOrderMapper.selectHtOrderList(order);
        if (orderList.size()>0) {
            for (HtOrder htOrder : orderList) {
                htOrder.setOrderStatus(4L);
                htOrder.setUpdatedAt(new Date());
                htOrderMapper.updateHtOrder(htOrder);
            }
        }
    }

}
