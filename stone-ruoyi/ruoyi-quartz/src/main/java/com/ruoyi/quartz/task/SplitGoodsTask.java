package com.ruoyi.quartz.task;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.common.utils.bean.BeanUtils;
import com.ruoyi.system.domain.HtFeeRevenueConfig;
import com.ruoyi.system.domain.HtGoods;
import com.ruoyi.system.mapper.HtGoodsMapper;
import com.ruoyi.system.service.impl.ComputingConfigUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Description:  商品金额拆分定时任务
 * @Author: mlk
 * @Date: 2024/11/28 19:40
 */
@Slf4j
@Component("SplitGoodsTask")
public class SplitGoodsTask {

    @Resource
    public HtGoodsMapper htGoodsMapper;

    @Autowired
    private ComputingConfigUtils computingConfigUtils;

    public void splitGoods() {
        HtFeeRevenueConfig htFeeRevenueConfig = computingConfigUtils.getHtFeeRevenueConfig();
        BigDecimal decimal = new BigDecimal("15000");
        BigDecimal Price_increase_rate = htFeeRevenueConfig.getFeeAmount();
        List<HtGoods> list = htGoodsMapper.splitGoods();

        for (HtGoods htGoods : list) {
            // 每次循环重新创建对象，避免数据混淆
            HtGoods tmp = new HtGoods();
            HtGoods tmp2 = new HtGoods();

            BeanUtils.copyBeanProp(tmp, htGoods);
            BeanUtils.copyBeanProp(tmp2, htGoods);

            tmp.setPrice(decimal);
            tmp2.setPrice(htGoods.getPrice().subtract(decimal));

            tmp.setResalePrice(decimal.multiply(Price_increase_rate).add(decimal));
            tmp2.setResalePrice(htGoods.getResalePrice().subtract(decimal.multiply(Price_increase_rate).add(decimal)));

            String storeNameA = tmp.getStoreName() + "A";
            String storeNameB = tmp2.getStoreName() + "B";

            tmp.setStoreName(storeNameA);
            tmp2.setStoreName(storeNameB);

            log.info("石头拆分1 {}", tmp);
            log.info("石头拆分2 {}", tmp2);

            // 同时检查A和B是否已存在，只有两者都不存在时才进行拆分
            if (htGoodsMapper.selectOne(Wrappers.<HtGoods>query().eq("store_name", storeNameA)) == null
                && htGoodsMapper.selectOne(Wrappers.<HtGoods>query().eq("store_name", storeNameB)) == null) {
                htGoodsMapper.insertHtGoods(tmp);
                htGoodsMapper.insertHtGoods(tmp2);
                //进行逻辑删除
                htGoods.setIsDel(1);
                htGoodsMapper.updateHtGoodsByIsDel(htGoods);

                log.info("石头[{}]拆分成功，生成商品[{}]和[{}]", htGoods.getStoreName(), storeNameA, storeNameB);
            } else {
                log.info("石头[{}]已被拆分过，跳过拆分操作", htGoods.getStoreName());
            }
        }
    }
}
