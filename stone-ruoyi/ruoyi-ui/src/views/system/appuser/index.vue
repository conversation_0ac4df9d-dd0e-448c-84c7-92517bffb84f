<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="手机号" prop="phone">
        <el-input v-model="queryParams.phone" placeholder="请输入手机号" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="姓名" prop="realName">
        <el-input v-model="queryParams.realName" placeholder="请输入昵称" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>



    <el-table border v-loading="loading" :data="appuserList" @selection-change="handleSelectionChange">
      <el-table-column label="id" align="center" prop="id" />
      <el-table-column label="手机号" align="center" prop="phone" />
      <el-table-column label="头像" align="center" prop="headImg" width="100">
        <template slot-scope="scope">
          <image-preview :src="scope.row.headImg" :width="50" :height="50" />
        </template>
      </el-table-column>
      <el-table-column label="真实姓名" align="center" prop="realName" />
      <el-table-column label="推荐人手机号" align="center" prop="referrers" />
      <el-table-column label="余额" align="center" prop="balance" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="rechargeUpdate(scope.row)"
            v-hasPermi="['system:appuser:edit']">充值</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
            v-hasPermi="['system:appuser:remove']">删除</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleQueryPayments(scope.row)"
            v-hasPermi="['system:appuser:export']">查看</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改app用户对话框 -->
    <el-dialog title="收款信息" :visible.sync="open1" width="500px" append-to-body>
      <el-form ref="form" :model="paymentsForm" :rules="rules" label-width="80px">
        <el-form-item label="银行卡信息" prop="accountInfo">
          <el-input v-model="paymentsForm.accountInfo" readonly="" />
        </el-form-item>
        <el-form-item label="微信收款码" prop="chatImg">
          <image-preview :src="paymentsForm.chatImg == '暂无数据' ? '' : paymentsForm.chatImg" :width="300" :height="300" />
        </el-form-item>
        <el-form-item label="支付收款码" prop="alipayImg">
          <image-preview :src="paymentsForm.alipayImg" :width="300" :height="300" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="form.phone" readonly placeholder="请输入手机号" />
        </el-form-item>
        <el-form-item label="头像" prop="headImg">
          <el-image :src="form.headImg" style="width: 200px;height: 200px"></el-image>
        </el-form-item>
        <el-form-item label="昵称" prop="nickName">
          <el-input v-model="form.realName" readonly placeholder="请输入昵称" />
        </el-form-item>
        <el-form-item label="余额" prop="balance">
          <el-input v-model="form.balance" readonly placeholder="请输入需要充值的余额 " />
        </el-form-item>
        <el-form-item label="充值余额余额">
          <el-input v-model="form.payBalance" placeholder="请输入需要充值的余额 " />
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listAppuser, getAppuser, delAppuser, addAppuser, updateAppuser, selectAppUserPayments } from "@/api/system/appuser";

export default {
  name: "Appuser",
  dicts: ['user_type'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // app用户表格数据
      appuserList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      open1: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        phone: null,
        realName: null,
      },
      // 表单参数
      form: {},
      paymentsForm: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询app用户列表 */
    getList() {
      this.loading = true;
      listAppuser(this.queryParams).then(response => {
        this.appuserList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.open1 = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        phone: null,
        password: null,
        openId: null,
        headImg: null,
        nickName: null,
        sex: null,
        age: null,
        status: null,
        delFlag: null,
        loginIp: null,
        loginDate: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: null,
        transactionPassword: null,
        realName: null,
        referrers: null,
        balance: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加app用户";
    },
    /** 修改按钮操作 */
    rechargeUpdate(row) {
      this.reset();
      console.log(row)
      console.log(row.id)
      const id = row.id || this.ids
      getAppuser(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "充值余额";
      });
    },
    handleQueryPayments(row) {
      this.open1 = true;
      const userId = row.id
      console.log(userId)
      selectAppUserPayments(userId).then(response => {
        this.paymentsForm = response.data;
        this.open1 = true;
        // this.title = "修改app用户";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateAppuser(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addAppuser(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除app用户编号为"' + ids + '"的数据项？').then(function () {
        return delAppuser(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/appuser/export', {
        ...this.queryParams
      }, `appuser_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
