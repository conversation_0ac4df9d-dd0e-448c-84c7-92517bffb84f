<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="余额" prop="balance">
        <el-input v-model="queryParams.balance" placeholder="请输入余额" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="手续费" prop="price">
        <el-input v-model="queryParams.price" placeholder="请输入手续费" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="付款凭证" prop="uploadImg">
        <el-input v-model="queryParams.uploadImg" placeholder="请输入付款凭证" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="审核状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择审核状态" clearable>
          <el-option v-for="dict in dict.type.sys_audit_type" :key="dict.value" :label="dict.label"
            :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>


    <el-table border v-loading="loading" :data="auditList" @selection-change="handleSelectionChange">
      <el-table-column label="id" align="center" prop="id" />
      <el-table-column label="用户名称" align="center" prop="userName" />
      <el-table-column label="石头名称" align="center" prop="goodsName" />
      <el-table-column label="石头价格" align="center" prop="goodsPrice" />
      <el-table-column label="商品图片" align="center" prop="goodsImg" width="100">
        <template slot-scope="scope">
          <image-preview :src="scope.row.goodsImg" :width="50" :height="50" />
        </template>
      </el-table-column>
      <el-table-column label="余额" align="center" prop="balance">
        <template slot-scope="scope">
          <span v-if="scope.row.type === 0">没有勾选余额</span>
          <span v-else>{{ scope.row.balance }}</span>
        </template>
      </el-table-column>
      <el-table-column label="手续费" align="center" prop="price" />
      <el-table-column label="付款凭证" align="center" prop="uploadImg" width="100">
        <template slot-scope="scope">
          <image-preview :src="scope.row.uploadImg" :width="50" :height="50" v-if="scope.row.type != 2" />
          <span v-else>余额大于手续费</span>
        </template>
      </el-table-column>
      <el-table-column label="审核状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_audit_type" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate1(scope.row)"
            v-hasPermi="['system:audit:edit']" v-if="scope.row.status == 0">审核
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改石头委托审核对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listAudit, getAudit, delAudit, addAudit, updateAudit, audit, overrule } from "@/api/system/audit";

export default {
  name: "Audit",
  dicts: ['sys_audit_type'],
  data() {
    return {
      flag: true,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 石头委托审核表格数据
      auditList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        balance: null,
        price: null,
        uploadImg: null,
        status: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {}
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询石头委托审核列表 */
    getList() {
      this.loading = true;
      listAudit(this.queryParams).then(response => {
        this.auditList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        orderId: null,
        userId: null,
        goodsId: null,
        balance: null,
        price: null,
        uploadImg: null,
        status: null,
        createTime: null,
        updateTime: null,
        createBy: null,
        updateBy: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },

    /** 修改按钮操作 */
    handleUpdate1(row) {
      this.reset();
      this.flag = true;
      const id = row.id || this.ids;
      this.form.id = id;
      this.open = true;
      this.title = "该操作将审核通过";
    },
    // 驳回
    handleUpdate2(row) {
      this.reset();
      this.flag = false;
      const id = row.id || this.ids;
      this.form.id = id;
      this.open = true;
      this.title = "该操作将驳回";
    },
    /** 提交按钮 */
    submitForm() {
      if (this.flag) {
        this.form.status = 1;
        audit(this.form).then(response => {
          this.$modal.msgSuccess("审核成功");
          this.open = false;
          this.getList();
        });
      } else {
        this.form.status = 0;
        overrule(this.form).then(response => {
          this.$modal.msgSuccess("驳回成功");
          this.open = false;
          this.getList();
        });
      }
    }
  }
};
</script>
