package com.ruoyi.common.utils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

@Component
public class RedisLockUtils {
    @Autowired
    public  RedisTemplate redisTemplate;

    /**
     * 加锁
     *
     * @param key   redis主键
     * @param value 值
     */
    public boolean lock(String key, String value) {
        final boolean result = Boolean.TRUE.equals(redisTemplate.opsForValue().setIfAbsent("LOCK_KEY" + key, value));
        if (result) {
//            log.info("[redisTemplate redis]设置锁缓存 缓存  url:{} ", key);
        }
        return result;
    }

    /**
     * 加锁（带超时时间）
     *
     * @param key     redis主键
     * @param value   值
     * @param timeout 超时时间（秒）
     */
    public boolean lockWithTimeout(String key, String value, long timeout) {
        final boolean result = Boolean.TRUE.equals(
            redisTemplate.opsForValue().setIfAbsent("LOCK_KEY" + key, value, timeout, TimeUnit.SECONDS)
        );
        if (result) {
//            log.info("[redisTemplate redis]设置锁缓存 缓存  url:{} ", key);
        }
        return result;
    }

    /**
     * 解锁
     *
     * @param key redis主键
     */
    public boolean unlock(String key) {
        final boolean result = Boolean.TRUE.equals(redisTemplate.delete("LOCK_KEY" + key));
        if (result) {
//            log.info("[redisTemplate redis]释放锁 缓存  url:{}", key);
        }
        return result;
    }
}
