package com.ruoyi.system.utils;

import com.ruoyi.system.constants.BusinessConstants;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.UUID;
import java.util.concurrent.ThreadLocalRandom;

/**
 * 订单工具类
 * 
 * <AUTHOR>
 * @date 2024-12-11
 */
public class OrderUtils {
    
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
    
    /**
     * 生成订单ID
     * 格式：时间戳(14位) + 随机数(6位) + 用户ID后4位
     * 
     * @param userId 用户ID
     * @return 订单ID
     */
    public static String generateOrderId(Integer userId) {
        // 时间戳部分（14位）
        String timestamp = LocalDateTime.now().format(DATE_TIME_FORMATTER);
        
        // 随机数部分（6位）
        int randomNum = ThreadLocalRandom.current().nextInt(100000, 999999);
        
        // 用户ID后4位
        String userIdSuffix = String.format("%04d", userId % 10000);
        
        return timestamp + randomNum + userIdSuffix;
    }
    
    /**
     * 生成简单的订单ID（兼容原有逻辑）
     * 
     * @return 订单ID
     */
    public static String generateSimpleOrderId() {
        return UUID.randomUUID().toString().substring(0, BusinessConstants.ORDER_ID_LENGTH);
    }
    
    /**
     * 验证订单ID格式
     * 
     * @param orderId 订单ID
     * @return 是否有效
     */
    public static boolean isValidOrderId(String orderId) {
        if (orderId == null || orderId.trim().isEmpty()) {
            return false;
        }
        
        // 检查长度
        if (orderId.length() != BusinessConstants.ORDER_ID_LENGTH) {
            return false;
        }
        
        // 检查是否包含非法字符（这里简单检查是否包含字母数字和连字符）
        return orderId.matches("[a-zA-Z0-9-]+");
    }
    
    /**
     * 私有构造函数，防止实例化
     */
    private OrderUtils() {
        throw new IllegalStateException("Utility class");
    }
}
