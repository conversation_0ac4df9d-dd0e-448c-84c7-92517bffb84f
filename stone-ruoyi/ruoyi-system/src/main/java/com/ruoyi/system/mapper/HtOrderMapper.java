package com.ruoyi.system.mapper;

import com.ruoyi.system.domain.HtOrderRecord;
import com.ruoyi.system.domain.vo.HtOrderRecordVo;
import com.ruoyi.system.domain.vo.HtOrderSystemVo;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;

import java.util.Date;
import java.util.HashMap;
import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.system.domain.HtOrder;

/**
 * 订单Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-23
 */
@Mapper
public interface HtOrderMapper extends BaseMapper<HtOrder> {
    /**
     * 查询订单
     *
     * @param id 订单主键
     * @return 订单
     */
    public HtOrder selectHtOrderById(Long id);

    /**
     * 查询订单列表
     *
     * @param htOrder 订单
     * @return 订单集合
     */
    public List<HtOrder> selectHtOrderList(HtOrder htOrder);
    public List<HtOrderSystemVo> selectSysHtOrderList(HtOrder htOrder);

    /**
     * 新增订单
     *
     * @param htOrder 订单
     * @return 结果
     */
    public int insertHtOrder(HtOrder htOrder);

    /**
     * 修改订单
     *
     * @param htOrder 订单
     * @return 结果
     */
    public int updateHtOrder(HtOrder htOrder);

    /**
     * 删除订单
     *
     * @param id 订单主键
     * @return 结果
     */
    public int deleteHtOrderById(Long id);

    /**
     * 批量删除订单
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteHtOrderByIds(Long[] ids);

    /**
     * 根据商品ID查询最新的订单记录
     *
     * @param goodsId 商品Id
     * @return 结果
     */
    public HtOrder selectByGoodIdLast(Long goodsId);

    List<HtOrder> selectOldList(Long id);

    List<HtOrderSystemVo> selectHtOrderListAndSysUser(HtOrder orderQuery);
}

