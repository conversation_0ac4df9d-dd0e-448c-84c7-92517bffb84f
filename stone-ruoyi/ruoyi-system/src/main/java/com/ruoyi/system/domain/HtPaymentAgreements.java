package com.ruoyi.system.domain;

//添加mybatisplus的三个包引用
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

//如果自己的包名修改了，则需要改成对应的包名
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 签约对象 ht_payment_agreements
 *
 * <AUTHOR>
 * @date 2024-11-27
 */
@Data
@TableName("ht_payment_agreements")
public class HtPaymentAgreements implements Serializable {

    private static final long serialVersionUID=1L;

    /** 签约id */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 管理员id */
    private Long sysUserId;

    /** 用户id */
    private Long userId;

    /** 支付方式id */
    @Excel(name = "支付方式id")
    private Long methodId;

    /** 支付方式的账户信息(银行卡号、支付账号) */
    @Excel(name = "支付方式的账户信息(银行卡号、支付账号)")
    private String accountInfo;

    /** 签约状态 */
    @Excel(name = "签约状态")
    private Long status;

    /** 签约时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "签约时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date signedAt;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updateAt;

    /** 银行名称 */
    @Excel(name = "银行名称")
    private String bankName;

    /** 微信收款图片 */
    @Excel(name = "微信收款图片")
    private String chatImg;

    /** 支付宝收款图片 */
    @Excel(name = "支付宝收款图片")
    private String alipayImg;

}

