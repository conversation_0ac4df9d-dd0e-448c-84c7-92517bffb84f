package com.ruoyi.system.utils;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.constants.BusinessConstants;
import com.ruoyi.system.domain.AppUser;
import com.ruoyi.system.domain.HtGoods;
import com.ruoyi.system.domain.HtOrder;
import com.ruoyi.system.enums.OrderStatusEnum;
import com.ruoyi.system.enums.OrderTypeEnum;
import com.ruoyi.system.mapper.AppUserMapper;
import com.ruoyi.system.mapper.HtGoodsMapper;
import com.ruoyi.system.mapper.HtOrderMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Slf4j
@Component
public class CreateOrderUtils {

    @Autowired
    private HtOrderMapper htOrderMapper;

    @Autowired
    private AppUserMapper appUserMapper;

    @Autowired
    private HtGoodsMapper goodsMapper;

    /**
     * 创建基础订单信息
     */
    public HtOrder createBaseOrder(Integer userId, HtGoods htGoods) {
        HtOrder htOrder = new HtOrder();
        htOrder.setGoodsId(htGoods.getId());
        htOrder.setOrderStatus(OrderStatusEnum.PENDING_REVIEW.getCode());
        htOrder.setGoodsPrice(htGoods.getPrice());
        htOrder.setUserId(Long.valueOf(userId));
        htOrder.setCreatedAt(new Date());
        htOrder.setUpdatedAt(new Date());
        htOrder.setOrderId(OrderUtils.generateOrderId(userId));
        return htOrder;
    }

    /**
     * 设置收款人信息
     */
    private void setRecipientInfo(HtOrder htOrder) {
        try {
            AppUser user = appUserMapper.selectAppUserById(htOrder.getRecipientId().longValue());
            if (user != null) {
                htOrder.setRecipientName(user.getRealName());
                htOrder.setRecipientPhone(user.getPhone());
            } else {
                log.warn("未找到收款人信息, userId: {}", htOrder.getRecipientId());
                htOrder.setRecipientName("未知用户");
                htOrder.setRecipientPhone("");
            }
        } catch (Exception e) {
            log.error("设置收款人信息异常, userId: {}", htOrder.getRecipientId(), e);
            htOrder.setRecipientName("未知用户");
            htOrder.setRecipientPhone("");
        }
    }

    /**
     * 获取用户商品列表
     */
    public List<HtGoods> getUserGoodsList(Integer userId) {
        return goodsMapper.selectList(Wrappers.<HtGoods>query()
                .eq("user_id", userId)
                .eq("is_del", BusinessConstants.GOODS_NOT_DELETED));
    }

    /**
     * 计算用户商品总价值
     */
    public BigDecimal calculateUserTotalPrice(List<HtGoods> userGoodsList) {
        return userGoodsList.stream()
                .map(HtGoods::getPrice)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 设置订单金额和类型
     */
    public void setOrderAmountAndType(HtOrder htOrder, HtGoods targetGoods,BigDecimal userTotalPrice) {
        //目标商品价格
        BigDecimal targetPrice = targetGoods.getPrice();

        // 计算价格差异
        BigDecimal priceDifference = userTotalPrice.subtract(targetPrice);
        htOrder.setAmount(priceDifference.abs());

        // 根据价格比较设置订单类型
        int comparison = userTotalPrice.compareTo(targetPrice);
        OrderTypeEnum orderType = OrderTypeEnum.getByComparison(comparison);
        htOrder.setOrderType(orderType.getCode());
        //需要付款
        if(htOrder.getOrderType()==1){
            //收款人信息，查询上一次商品属于哪个用户的订单，找到对应的商家为其付款
            HtOrder htOrderLast=htOrderMapper.selectByGoodIdLast(targetGoods.getId());
            if(StringUtils.isNotNull(htOrderLast)){
                htOrder.setRecipientId(htOrderLast.getRecipientId());
            }else{
                htOrder.setRecipientId(targetGoods.getUserId());
            }

            // 设置收款人信息
            setRecipientInfo(htOrder);
        }
        if(htOrder.getOrderType()==0){
            Long userId=htOrder.getUserId();
            //需要收款时，将收款人设置为自己，付款人随后匹配
            htOrder.setRecipientId(userId.intValue());
            htOrder.setUserId(null);
            // 设置收款人信息
            setRecipientInfo(htOrder);
        }
    }
}
