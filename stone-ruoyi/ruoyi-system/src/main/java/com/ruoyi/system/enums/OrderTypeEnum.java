package com.ruoyi.system.enums;

/**
 * 订单类型枚举
 * 
 * <AUTHOR>
 * @date 2024-12-11
 */
public enum OrderTypeEnum {
    
    /**
     * 收款 - 用户现有商品价值大于目标商品价值
     */
    RECEIVE_PAYMENT(0, "收款"),
    
    /**
     * 付款 - 用户现有商品价值小于目标商品价值
     */
    MAKE_PAYMENT(1, "付款"),
    
    /**
     * 直接调换 - 用户现有商品价值等于目标商品价值
     */
    DIRECT_EXCHANGE(3, "直接调换");
    
    private final Integer code;
    private final String description;
    
    OrderTypeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public Integer getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据价格比较结果获取订单类型
     * 
     * @param comparison 价格比较结果 (userPrice.compareTo(targetPrice))
     * @return 订单类型
     */
    public static OrderTypeEnum getByComparison(int comparison) {
        if (comparison < 0) {
            return MAKE_PAYMENT;
        } else if (comparison == 0) {
            return DIRECT_EXCHANGE;
        } else {
            return RECEIVE_PAYMENT;
        }
    }
}
