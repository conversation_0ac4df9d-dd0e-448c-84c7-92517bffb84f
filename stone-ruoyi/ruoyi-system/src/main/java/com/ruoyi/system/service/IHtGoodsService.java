package com.ruoyi.system.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.system.domain.HtGoods;

/**
 * 商品Service接口
 *
 * <AUTHOR>
 * @date 2024-11-21
 */
public interface IHtGoodsService extends IService<HtGoods> {
    /**
     * 查询商品
     *
     * @param id 商品主键
     * @return 商品
     */
    public HtGoods selectHtGoodsById(Long id);

    /**
     * 查询商品列表
     *
     * @param htGoods 商品
     * @return 商品集合
     */
    public List<HtGoods> selectHtGoodsList(HtGoods htGoods);

    /**
     * 新增商品
     *
     * @param htGoods 商品
     * @return 结果
     */
    public int insertHtGoods(HtGoods htGoods);

    /**
     * 修改商品
     *
     * @param htGoods 商品
     * @return 结果
     */
    public int updateHtGoods(HtGoods htGoods);

    /**
     * 批量删除商品
     *
     * @param ids 需要删除的商品主键集合
     * @return 结果
     */
    public int deleteHtGoodsByIds(Long[] ids);

    /**
     * 删除商品信息
     *
     * @param id 商品主键
     * @return 结果
     */
    public int deleteHtGoodsById(Long id);


    //AjaxResult snappedGoods(Integer userId, Integer goodsId);

    AjaxResult snappedGoodsOptimized(Integer userId, Integer goodsId);

    List<HtGoods> selectSystemHtGoodsList(HtGoods htGoods);

    int insertSystemHtGoods(HtGoods htGoods);

    List<HtGoods> selectMyHtGoodsList(Long userId);
}

