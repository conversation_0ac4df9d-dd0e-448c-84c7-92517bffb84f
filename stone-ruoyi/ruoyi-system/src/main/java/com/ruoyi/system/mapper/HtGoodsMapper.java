package com.ruoyi.system.mapper;

import org.apache.ibatis.annotations.Mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.system.domain.HtGoods;

/**
 * 商品Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-21
 */
@Mapper
public interface HtGoodsMapper extends BaseMapper<HtGoods> {
    /**
     * 查询商品
     *
     * @param id 商品主键
     * @return 商品
     */
    public HtGoods selectHtGoodsById(Long id);

    /**
     * 查询商品列表
     *
     * @param htGoods 商品
     * @return 商品集合
     */
    public List<HtGoods> selectHtGoodsList(HtGoods htGoods);

    /**
     * 新增商品
     *
     * @param htGoods 商品
     * @return 结果
     */
    public int insertHtGoods(HtGoods htGoods);

    /**
     * 修改商品
     *
     * @param htGoods 商品
     * @return 结果
     */
    public int updateHtGoods(HtGoods htGoods);

    /**
     * 删除商品
     *
     * @param id 商品主键
     * @return 结果
     */
    public int deleteHtGoodsById(Long id);

    /**
     * 批量删除商品
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteHtGoodsByIds(Long[] ids);

    /**
     * 查询需要拆分的商品
     *
     * @param
     * @return 结果
     */
    List<HtGoods> splitGoods();
    /**
     * 商品逻辑删除
     */
    int updateHtGoodsByIsDel(HtGoods htGoods);
    /**
     * 根据用户主键查询商品列表
     * @param userId 用户主键
     * @return 结果
     */
    List<HtGoods> selectMyHtGoodsList(Long userId);

    List<HtGoods> selectSystemHtGoodsList(HtGoods htGoods);
}

