package com.ruoyi.system.enums;

/**
 * 订单状态枚举
 * 
 * <AUTHOR>
 * @date 2024-12-11
 */
public enum OrderStatusEnum {
    
    /**
     * 待审核
     */
    PENDING_REVIEW(1L, "待审核"),
    
    /**
     * 待完成订单
     */
    SPECIAL_STATUS(2L, "待完成订单"),
    
    /**
     * 不需要付款 - 价格相等直接调换
     */
    NO_PAYMENT_REQUIRED(3L, "不需要付款"),
    
    /**
     * 已付款
     */
    PAID(9L, "已付款");
    
    private final Long code;
    private final String description;
    
    OrderStatusEnum(Long code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public Long getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
}
